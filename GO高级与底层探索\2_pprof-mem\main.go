package main

import (
	"fmt"
	"net/http"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
)

// ★ 模拟内存泄漏的全局变量 ★
var leakyData [][]byte

// leakyEndpointHandler 每次调用都会分配1MB内存并泄漏
func leakyEndpointHandler(c *gin.Context) {
	data := make([]byte, 1024*1024)     //分配1MB内存
	leakyData = append(leakyData, data) //将内存追加到全局变量中，导致内存泄漏
	c.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("成功泄露1MB内存,当前总泄漏量: %d MB", len(leakyData))})
}

func main() {
	// 设置Gin为release模式
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	pprof.Register(r)
	// ★★★ 注册我们的“内存泄漏”路由 ★★★
	r.GET("/leak", leakyEndpointHandler)
	r.Run(":8080")
}
