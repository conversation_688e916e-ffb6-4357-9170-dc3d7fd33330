package main

import (
	"crypto/sha256"
	"fmt"
	"net/http"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	db *gorm.DB
}

func helloHandler(c *gin.Context) {
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{"message": "Hello, World!"})
}

// 一个故意设计得很慢的处理器，用于性能测试
func (s *Server) slowEndpointHandler(c *gin.Context) {
	//进行大量哈希计算来消耗cpu
	sum := []byte{} //byte类型的切片
	for i := range 500000 {
		h := sha256.New()
		//type byte = uint8
		h.Write([]byte(fmt.Sprintf("some data %d", i))) //将字符串转换为字节切片
		sum = h.Sum(nil)                                //将哈希值写入sum
	}
	c.JSON(http.StatusOK, gin.H{"status": "done", "hash": fmt.Sprintf("%x", sum)})
}

func main() {
	// 设置Gin为release模式
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()
	pprof.Register(r)
	r.GET("/hello", helloHandler)
	r.Run(":8080")
}
